package service

import (
	"context"
	"encoding/json"
	"github.com/avito-tech/go-transaction-manager/trm/v2"
	"github.com/feedspring/feedspring-api/apps/api/internal/config"
	"github.com/feedspring/feedspring-api/apps/api/internal/dto"
	"github.com/feedspring/feedspring-api/apps/api/internal/dto/out"
	"github.com/feedspring/feedspring-api/apps/api/internal/entity"
	"github.com/feedspring/feedspring-api/apps/api/internal/errors"
	"github.com/feedspring/feedspring-api/apps/api/internal/providers"
	instagramoauthrepo "github.com/feedspring/feedspring-api/apps/api/internal/repository/instagram-oauth"
	"github.com/feedspring/feedspring-api/apps/api/pkg/cache"
	"github.com/feedspring/feedspring-api/apps/api/pkg/instagram"
	"github.com/feedspring/feedspring-api/apps/api/pkg/logger"
	"github.com/feedspring/feedspring-api/apps/api/pkg/tokens"
	"github.com/feedspring/feedspring-api/apps/api/pkg/validator"
	"github.com/feedspring/feedspring-storage/proto/pb"
	"github.com/jmoiron/sqlx"
	"github.com/jmoiron/sqlx/types"
	"golang.org/x/sync/errgroup"
	"time"
)

type Service interface {
	providers.Provider
}

type instagramOauthService struct {
	providers.Provider

	client     instagram.Client
	repository instagramoauthrepo.Repository
	trmManger  trm.Manager

	accountsService AccountsService
	imagesService   pb.ImagesServiceClient
	config          *config.Config

	tokens *tokens.Client
	logger *logger.Client
}

func New(
	repository instagramoauthrepo.Repository,
	accountsService AccountsService,
	imagesService pb.ImagesServiceClient,
	trmManger trm.Manager,
	cache *cache.Client,
	logger *logger.Client,
	config *config.Config,
) Service {
	return &instagramOauthService{
		client: instagram.New(
			instagram.Config{
				ClientID:     config.Services.InstagramOauth.ClientKey,
				ClientSecret: config.Services.InstagramOauth.SecretKey,
			},
		),

		repository: repository,

		accountsService: accountsService,
		imagesService:   imagesService,

		tokens:    tokens.NewTokens("instagram_oauth", cache, logger),
		logger:    logger,
		trmManger: trmManger,
		config:    config,

		Provider: providers.NewBaseProvider(
			providers.Config{
				Name:   providers.InstagramOauth,
				Prefix: "instagram_oauth",
				SupportedTypes: []entity.AccountType{
					entity.AccountTypeAttrs,
					entity.AccountTypeFramer,
					entity.AccountTypeWebflow,
				},
			},
		),
	}
}

func (s *instagramOauthService) ValidateOAuthOptions(ctx context.Context, options types.JSONText) (interface{}, error) {
	var optionsDTO dto.InstagramOAuthOptionsDTO

	if err := json.Unmarshal(options, &optionsDTO); err != nil {
		return nil, errors.NewWrap(err, "Failed to parse options")
	}

	if err := validator.ValidateStruct(&optionsDTO); err != nil {
		return nil, err
	}

	return &optionsDTO, nil
}

func (s *instagramOauthService) OAuth(ctx context.Context, userID int, options interface{}) (string, error) {
	oauthOptions, ok := options.(*dto.InstagramOAuthOptionsDTO)
	if !ok {
		return "", errors.New("OAuth options not found")
	}

	token, err := s.tokens.CreateToken(
		ctx, &tokens.CreateTokenDTO{
			Expiration: time.Minute * 15,
			Type:       "account_connect",
			UserID:     userID,
			Meta: dto.TokenMapDTO{
				"source":       s.GetName(),
				"redirect_uri": oauthOptions.CallbackURL,
			},
		},
	)
	if err != nil {
		return "", err
	}

	return s.client.GetAuthorizationURL(token.ID, oauthOptions.CallbackURL), nil
}

func (s *instagramOauthService) ValidateConnectOptions(ctx context.Context, options types.JSONText) (interface{}, error) {
	var optionsDTO dto.InstagramOAuthConnectOptionsDTO

	if err := json.Unmarshal(options, &optionsDTO); err != nil {
		return nil, errors.NewWrap(err, "Failed to parse options")
	}

	if err := validator.ValidateStruct(&optionsDTO); err != nil {
		return nil, err
	}

	return &optionsDTO, nil
}

func (s *instagramOauthService) Connect(
	ctx context.Context,
	tx *sqlx.Tx,
	accountID int,
	options interface{},
) (*providers.ConnectOptions, error) {
	connectOptions, ok := options.(*dto.InstagramOAuthConnectOptionsDTO)
	if !ok {
		return nil, errors.New("Connect options not found")
	}

	token := s.tokens.GetToken(ctx, connectOptions.State, "account_connect")
	if token == nil {
		return nil, errors.New("The token was not found or expired")
	}

	defer func() {
		s.tokens.DeleteToken(token)
	}()

	redirectURI, ok := token.Meta["redirect_uri"].(string)
	if !ok {
		return nil, errors.New("Invalid token meta data")
	}

	authToken, err := s.client.GetAuthTokenByCode(ctx, connectOptions.Code, redirectURI)
	if err != nil {
		s.logger.WithCtx(ctx).Error(err)
		return nil, err
	}

	profile, err := s.client.GetUserProfile(ctx, authToken.AccessToken)
	if err != nil {
		return nil, err
	}

	// TODO: refactor to new written pgx repository when flow PR get merged
	_, err = tx.ExecContext(ctx, `
INSERT INTO instagram_oauth_accounts (
    instagram_id,
    username,
    account_id,
    available_steps,
    access_token,
    expired_at
)
VALUES (
           $1,
           $2,
           $3,
           $4,
           PGP_SYM_ENCRYPT($5, $7),
           $6
       )
`,
		profile.ID,
		profile.Username,
		accountID, 0,
		authToken.AccessToken,
		time.Now().Add(time.Second*time.Duration(authToken.ExpiresIn)),
		s.config.EncryptKeys.InstagramOauth,
	)
	if err != nil {
		return nil, err
	}

	// TODO: refactor to new written pgx repository when flow PR get merged
	//newEntity := entity.InstagramOAuthAccount{
	//	InstagramID:    profile.ID,
	//	Username:       profile.Username,
	//	AccountID:      accountID,
	//	AccessToken:    authToken.AccessToken,
	//	RefreshToken:   authToken.RefreshToken,
	//	ExpiredAt:      time.Now().Add(time.Second * time.Duration(authToken.ExpiresIn)),
	//	AvailableSteps: 0,
	//}
	//
	//if err := s.repository.CreateInstagramOAuth(ctx, &newEntity); err != nil {
	//	return nil, err
	//}

	status := entity.AccountStatusActive

	return &providers.ConnectOptions{
		Status: &status,
		Name:   profile.Username,
	}, nil
}

func (s *instagramOauthService) Get(ctx context.Context, accountID int) (interface{}, error) {
	instagramAccount, err := s.repository.GetInstagramOAuthByAccountID(ctx, accountID)
	if err != nil {
		return nil, err
	}

	return out.NewInstagramOAuthOptionsResponse(instagramAccount), nil
}

func (s *instagramOauthService) Update(
	ctx context.Context,
	_ *sqlx.Tx,
	account *entity.Account,
	data types.JSONText,
) (interface{}, error) {
	instagramAccount, err := s.repository.GetInstagramOAuthByAccountID(ctx, account.ID)
	if err != nil {
		return nil, err
	}

	if instagramAccount == nil {
		return nil, errors.New("Instagram oauth account not found")
	}

	var updatedOptions dto.InstagramOAuthUpdateOptionsDTO
	if err := json.Unmarshal(data, &updatedOptions); err != nil {
		s.logger.WithCtx(ctx).Error(err)
		return nil, err
	}

	var update bool
	if updatedOptions.AvailableSteps.Set {
		update = true
		instagramAccount.AvailableSteps = int(updatedOptions.AvailableSteps.Int64)
	}

	if update {
		if err := s.repository.UpdateInstagramOAuth(ctx, instagramAccount); err != nil {
			return nil, err
		}
	}

	return out.NewInstagramOAuthOptionsResponse(instagramAccount), nil
}

func (s *instagramOauthService) IsAvailableForActivation(ctx context.Context, accountID int) (
	bool, error,
) {
	return true, nil
}

func (s *instagramOauthService) OnResetType(
	ctx context.Context,
	tx *sqlx.Tx,
	accountID int,
	accountType entity.AccountType,
) error {
	instagramEntity, err := s.repository.GetInstagramOAuthByAccountID(ctx, accountID)
	if err != nil {
		return err
	}

	instagramEntity.AvailableSteps = 0

	if err := s.repository.UpdateInstagramOAuth(ctx, instagramEntity); err != nil {
		return err
	}

	return nil
}

func (s *instagramOauthService) getEntityByAccountIDWithRefresh(ctx context.Context, accountID int) (*entity.InstagramOAuthAccount, error) {
	instagramEntity, err := s.repository.GetInstagramOAuthByAccountID(ctx, accountID)
	if err != nil {
		return nil, err
	}

	if !instagramEntity.IsAccessTokenExpired() {
		return instagramEntity, nil
	}

	authToken, err := s.client.RefreshAccessToken(ctx, instagramEntity.AccessToken)
	if err != nil {
		return nil, err
	}

	instagramEntity.AccessToken = authToken.AccessToken
	instagramEntity.ExpiredAt = time.Now().Add(time.Second * time.Duration(authToken.ExpiresIn))

	if err := s.repository.UpdateInstagramOAuth(ctx, instagramEntity); err != nil {
		return nil, err
	}

	return instagramEntity, nil
}

func (s *instagramOauthService) GetMedia(
	ctx context.Context,
	getMediaDTO *dto.GetMediaInput,
) (interface{}, error) {
	instagramEntity, err := s.getEntityByAccountIDWithRefresh(ctx, getMediaDTO.AccountID)
	if err != nil {
		return nil, err
	}

	wg, wgCtx := errgroup.WithContext(ctx)

	response := out.InstagramMediaResponse{}

	wg.Go(func() error {
		profile, err := s.client.GetUserProfile(wgCtx, instagramEntity.AccessToken)
		if err != nil {
			return err
		}

		response.Exta = out.InstagramExtra{
			Bio:            profile.Biography,
			FollowersCount: profile.FollowersCount,
			FollowingCount: profile.FollowingCount,
			Avatar:         profile.ProfilePicURL,
			FullName:       profile.Name,
			Username:       profile.Username,
			Name:           profile.Name,
		}
		return nil
	})

	wg.Go(func() error {
		media, err := s.client.GetUserMedia(wgCtx, instagramEntity.AccessToken, getMediaDTO.Items)
		if err != nil {
			return err
		}

		response.Media = make([]*out.InstagramItem, len(media.Data))
		for i, m := range media.Data {
			childs := make([]*out.InstagramChild, len(m.Children))
			for j, c := range m.Children {
				childs[j] = &out.InstagramChild{
					ID:        c.ID,
					MediaType: c.MediaType,
					MediaUrl:  c.MediaURL,
				}
			}

			response.Media[i] = &out.InstagramItem{
				ID:           m.ID,
				MediaType:    m.MediaType,
				MediaUrl:     m.MediaURL,
				Permalink:    m.Permalink,
				LikeCount:    m.LikeCount,
				CommentCount: m.CommentsCount,
				Timestamp:    m.Timestamp,
				Caption:      m.Caption,
				Children:     childs,
			}
		}

		return nil
	})

	if err := wg.Wait(); err != nil {
		return nil, err
	}

	return response, nil
}

func (s *instagramOauthService) FilterMedia(
	ctx context.Context,
	filterMediaDTO *dto.FilterMediaDTO,
) (interface{}, error) {
	response, ok := filterMediaDTO.Media.(*out.InstagramMediaResponse)

	if !ok {
		return nil, nil
	}

	list := []*out.InstagramItem{}

	for _, item := range response.Media {
		if len(list) >= filterMediaDTO.Items {
			break
		}

		list = append(list, item)
	}

	response.Media = list

	return response, nil
}

func (s *instagramOauthService) ParseMedia(ctx context.Context, media string) (interface{}, error) {
	var result out.InstagramMediaResponse

	if err := json.Unmarshal([]byte(media), &result); err != nil {
		s.logger.
			WithCtx(ctx).
			Errorf("Error parse instagram media from cache: %s", err)

		return nil, err
	}

	return &result, nil
}
