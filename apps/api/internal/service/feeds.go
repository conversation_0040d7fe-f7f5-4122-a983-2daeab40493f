package service

import (
	"context"
	"encoding/json"
	"fmt"
	"github.com/feedspring/feedspring-api/apps/api/internal/service/views"
	"net/url"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/feedspring/feedspring-api/apps/api/internal/config"
	"github.com/feedspring/feedspring-api/apps/api/internal/dto"
	"github.com/feedspring/feedspring-api/apps/api/internal/dto/out"
	"github.com/feedspring/feedspring-api/apps/api/internal/entity"
	"github.com/feedspring/feedspring-api/apps/api/internal/errors"
	"github.com/feedspring/feedspring-api/apps/api/internal/providers"
	"github.com/feedspring/feedspring-api/apps/api/internal/queue/tasks"
	"github.com/feedspring/feedspring-api/apps/api/pkg/cache"
	"github.com/feedspring/feedspring-api/apps/api/pkg/database"
	"github.com/feedspring/feedspring-api/apps/api/pkg/logger"

	"github.com/jmoiron/sqlx"
	"github.com/jmoiron/sqlx/types"

	viewsservice "github.com/feedspring/feedspring-api/apps/api/internal/service/views"
)

type feedsService struct {
	googleService         GoogleReviewsService
	instagramService      InstagramService
	tiktokService         TiktokService
	dribbbleService       DribbbleService
	accountsService       AccountsService
	viewsService          views.Service
	plansService          PlansService
	usersService          UsersService
	instagramOauthService Service

	ignoreHostNames []string

	cache  *cache.Client
	logger *logger.Client
	db     *database.Client
	tasks  *tasks.FeedsTasks
}

type FeedsService interface {
	GetFeedMedia(ctx context.Context, publicKey, origin string) (interface{}, error)
	GetFeeds(ctx context.Context, userID int) (out.FeedsListDTO, error)
	GetFeed(ctx context.Context, userID int, accountUUID string) (*out.FeedDetailDTO, error)

	Connect(ctx context.Context, userID int, connectFeedDTO *dto.ConnectFeedDTO) (
		*entity.Account, error,
	)
	OAuth(ctx context.Context, userID int, oauthFeedDTO *dto.OAuthFeedDTO) (string, error)

	UpdateFeedData(ctx context.Context, accountID int) error
	UpdateFeed(
		ctx context.Context,
		userID int,
		accountUUID string,
		updateFeedDTO *dto.UpdateFeedDTO,
	) (*out.FeedDetailDTO, error)
	ResetFeedType(ctx context.Context, userID int, accountUUID string) error
	ResetFeedPublicKey(ctx context.Context, userID int, accountUUID string) (string, error)
	ActivateFeed(ctx context.Context, userID int, accountUUID string) error
	DeleteFeed(ctx context.Context, userID int, accountUUID string) error
}

func NewFeedsService(
	googleService GoogleReviewsService,
	instagramService InstagramService,
	accountsService AccountsService,
	tiktokService TiktokService,
	dribbbleService DribbbleService,
	viewsService views.Service,
	plansService PlansService,
	usersService UsersService,
	instagramOauthService Service,

	config *config.Config,
	logger *logger.Client,
	cache *cache.Client,
	db *database.Client,
	tasks *tasks.FeedsTasks,
) FeedsService {
	return &feedsService{
		instagramService:      instagramService,
		accountsService:       accountsService,
		tiktokService:         tiktokService,
		googleService:         googleService,
		dribbbleService:       dribbbleService,
		viewsService:          viewsService,
		plansService:          plansService,
		usersService:          usersService,
		instagramOauthService: instagramOauthService,

		ignoreHostNames: config.App.Feeds.Views.IgnoreHostNames,

		logger: logger,
		cache:  cache,
		db:     db,
		tasks:  tasks,
	}
}

func (s *feedsService) getProvider(source string) (providers.Provider, error) {
	switch source {
	case providers.InstagramSource:
		return s.instagramService, nil
	case providers.GoogleReviewsSource:
		return s.googleService, nil
	case providers.TiktokSource:
		return s.tiktokService, nil
	case providers.DribbbleSource:
		return s.dribbbleService, nil
	case providers.InstagramOauth:
		return s.instagramOauthService, nil
	default:
		return nil, errors.New(`The provider for the source "%s" was not found`, source)
	}
}

func (s *feedsService) isIgnoreOrigin(origin string) bool {
	if origin != "" {
		originURL, _ := url.Parse(origin)

		if originURL != nil {
			hostname := originURL.Hostname()

			for _, ignoreHostname := range s.ignoreHostNames {
				if strings.HasSuffix(hostname, ignoreHostname) {
					return true
				}
			}
		}
	}

	return false
}

func (s *feedsService) GetFeedMedia(ctx context.Context, publicKey, origin string) (
	interface{}, error,
) {
	account, err := s.accountsService.GetAccountByPublicKey(ctx, publicKey)
	if err != nil {
		return nil, err
	}

	if account.Status != entity.AccountStatusActive {
		return nil, errors.New("The feed is not active").SetCode(errors.ErrorCodeFeedNotActive)
	}

	viewsCount, err := s.viewsService.GetViewsByUserID(ctx, account.UserID)
	if err != nil {
		return nil, err
	}

	planUser, err := s.plansService.GetPlanByUserID(ctx, account.UserID)
	if err != nil {
		return nil, err
	}

	if viewsCount >= planUser.Restrictions.Views {
		return nil, errors.ErrorLimitReached
	}

	if len(account.DomainWhitelist) > 0 {

		var isExist bool

		for _, domain := range account.DomainWhitelist {
			if origin == domain {
				isExist = true
				break
			}
		}

		if !isExist {
			errMsg := "Origin%s is not allowed"

			if origin != "" {
				errMsg = fmt.Sprintf(errMsg, " "+origin)
			} else {
				errMsg = fmt.Sprintf(errMsg, "")
			}

			return nil, errors.New(errMsg).SetCode(errors.ErrorNotAllowed)
		}
	}

	if !s.isIgnoreOrigin(origin) {
		go func() {
			ctx, cancel := context.WithTimeout(context.WithoutCancel(ctx), 5*time.Second)
			defer cancel()

			createErr := s.viewsService.Create(
				ctx,
				viewsservice.CreateInput{
					UserPlanLimit: planUser.Restrictions.Views,
					UserID:        account.UserID,
					FeedID:        &account.ID,
				},
			)

			if createErr != nil {
				s.logger.
					WithError(createErr).
					WithField("feed_id", account.ID).
					Error("Failed to create view")
			}
		}()
	}

	provider, err := s.getProvider(account.Provider)
	if err != nil {
		return nil, err
	}

	var media interface{}

	if account.Data != nil {
		media, err = provider.ParseMedia(ctx, string(*account.Data))

		if err != nil {
			return nil, err
		}
	}

	if media != nil {
		media, err = provider.FilterMedia(
			ctx, &dto.FilterMediaDTO{
				Media:     media,
				Items:     account.Items,
				AccountID: account.ID,
			},
		)

		if err != nil {
			return nil, err
		}
	}

	update := account.DataUpdatedAt == nil

	if !update && account.DataUpdatedAt.Add(
		planUser.Restrictions.GetCache(provider.GetName()),
	).Before(time.Now()) {
		update = true
	}
	if update {
		go s.tasks.UpdateFeedData(account.ID)
	}

	return media, nil
}

func (s *feedsService) GetFeeds(ctx context.Context, userID int) (out.FeedsListDTO, error) {
	accounts, err := s.accountsService.GetAccountsByUserID(ctx, userID)

	if err != nil {
		return nil, err
	}

	return out.NewFeedsListDTO(accounts), nil
}

func (s *feedsService) OAuth(
	ctx context.Context,
	userID int,
	oauthFeedDTO *dto.OAuthFeedDTO,
) (string, error) {
	user, err := s.usersService.GetUserByID(ctx, userID)

	if err != nil {
		return "", err
	}

	if err := user.IsEmailConfirmed(); err != nil {
		return "", err
	}

	userPlan, err := s.plansService.GetPlanByUserID(ctx, userID)

	if err != nil {
		return "", err
	}

	accountsCount, err := s.accountsService.GetCountAccountsByUserID(ctx, userID)

	if err != nil {
		return "", err
	}

	if accountsCount >= userPlan.Restrictions.Feeds {
		return "", errors.ErrorLimitReached
	}

	provider, err := s.getProvider(oauthFeedDTO.Source)

	if err != nil {
		return "", err
	}

	options, err := provider.ValidateOAuthOptions(ctx, oauthFeedDTO.Options)

	if err != nil {
		return "", err
	}

	u, err := provider.OAuth(ctx, userID, options)

	if err != nil {
		return "", err
	}

	return u, nil
}

func (s *feedsService) Connect(
	ctx context.Context,
	userID int,
	connectFeedDTO *dto.ConnectFeedDTO,
) (*entity.Account, error) {
	user, err := s.usersService.GetUserByID(ctx, userID)

	if err != nil {
		return nil, err
	}

	if err := user.IsEmailConfirmed(); err != nil {
		return nil, err
	}

	userPlan, err := s.plansService.GetPlanByUserID(ctx, userID)

	if err != nil {
		return nil, err
	}

	accountsCount, err := s.accountsService.GetCountAccountsByUserID(ctx, userID)

	if err != nil {
		return nil, err
	}

	if accountsCount >= userPlan.Restrictions.Feeds {
		return nil, errors.ErrorLimitReached
	}

	provider, err := s.getProvider(connectFeedDTO.Source)

	if err != nil {
		return nil, err
	}

	options, err := provider.ValidateConnectOptions(ctx, connectFeedDTO.Options)

	if err != nil {
		return nil, err
	}

	var account *entity.Account

	if err := database.WithTransaction(
		s.db, ctx, func(tx *sqlx.Tx) error {
			var err error

			providerName := provider.GetName()

			account, err = s.accountsService.CreateAccountTx(
				ctx, tx, &dto.CreateAccountDTO{
					Provider: providerName,
					Prefix:   provider.GetPrefix(),
					Status:   entity.AccountStatusConfigure,

					UserID: userID,

					Items: userPlan.Restrictions.GetMaxItems(providerName),
				},
			)

			if err != nil {
				return err
			}

			connectOptions, err := provider.Connect(ctx, tx, account.ID, options)

			if err != nil {
				return err
			}

			if connectOptions.Name != "" {
				account.Name = &connectOptions.Name
			}

			if connectOptions.Status != nil {
				account.Status = *connectOptions.Status
			}

			if connectOptions.Data != nil {
				data, err := json.Marshal(connectOptions.Data)

				if err != nil {
					return err
				}

				accountData := types.JSONText(data)
				accountDataUpdatedAt := time.Now()

				account.Data = &accountData
				account.DataUpdatedAt = &accountDataUpdatedAt
			}

			// TODO:
			account.DomainWhitelist = entity.DomainWhitelist{}

			err = s.accountsService.UpdateAccountTx(ctx, tx, account)

			if err != nil {
				return err
			}

			return nil
		},
	); err != nil {
		switch e := err.(type) {
		case *errors.Error:
			return nil, e
		default:
			s.logger.WithCtx(ctx).WithError(err).Error("Failed to connect account")
			return nil, errors.New(`Failed to connect %s accounts`, provider.GetName())
		}
	}

	return account, nil
}

func (s *feedsService) ResetFeedPublicKey(
	ctx context.Context,
	userID int,
	accountUUID string,
) (string, error) {
	account, err := s.accountsService.GetAccountByUUID(ctx, userID, accountUUID)

	if err != nil {
		return "", err
	}

	provider, err := s.getProvider(account.Provider)

	if err != nil {
		return "", err
	}

	if err := s.accountsService.ResetAccountPublicKey(
		ctx,
		account,
		provider.GetPrefix(),
	); err != nil {
		return "", err
	}

	return account.PublicKey, nil
}

func (s *feedsService) GetFeed(
	ctx context.Context,
	userID int,
	accountUUID string,
) (*out.FeedDetailDTO, error) {
	account, err := s.accountsService.GetAccountByUUID(ctx, userID, accountUUID)

	if err != nil {
		return nil, err
	}

	provider, err := s.getProvider(account.Provider)

	if err != nil {
		return nil, err
	}

	data, err := provider.Get(ctx, account.ID)

	if err != nil {
		return nil, err
	}

	return out.NewFeedDetailDTO(account, provider.GetSupportedTypes(), data), nil
}

func (s *feedsService) DeleteFeed(ctx context.Context, userID int, accountUUID string) error {
	err := s.accountsService.DeleteAccountByUUID(ctx, userID, accountUUID)

	if err != nil {
		return err
	}

	plan, err := s.plansService.GetPlanByUserID(ctx, userID)

	if err != nil {
		return err
	}

	accounts, err := s.accountsService.GetAccountsByUserID(ctx, userID)

	if err != nil {
		return err
	}

	var activeAccountsCount int

	for _, account := range accounts {
		if account.Status == entity.AccountStatusActive || account.Status == entity.AccountStatusConfigure {
			activeAccountsCount++
		}
	}

	accountsToUnblockCount := plan.Restrictions.Feeds - activeAccountsCount

	var wg sync.WaitGroup
	var mu sync.Mutex

	for _, account := range accounts {
		mu.Lock()

		if accountsToUnblockCount > 0 && account.Status == entity.AccountStatusInactive {
			accountsToUnblockCount--

			wg.Add(1)

			go func(account *entity.Account) {
				defer wg.Done()

				accountStatus := entity.AccountStatusActive

				if account.PrevStatus != nil {
					accountStatus = *account.PrevStatus
				}

				if err := s.accountsService.UpdateAccountStatus(
					ctx,
					account.ID,
					accountStatus,
					nil,
				); err != nil {
					s.logger.WithCtx(ctx).WithError(err).Error()
				}
			}(account)
		}

		mu.Unlock()
	}

	wg.Wait()

	return nil
}

func (s *feedsService) UpdateFeedData(ctx context.Context, accountID int) error {
	account, err := s.accountsService.GetAccountByID(ctx, accountID)

	if err != nil {
		return err
	}

	provider, err := s.getProvider(account.Provider)

	if err != nil {
		return err
	}

	media, err := provider.GetMedia(
		ctx, &dto.GetMediaInput{
			AccountID: accountID,
			Items:     account.Items,
		},
	)

	if err != nil {
		return err
	}

	if media != nil {
		data, err := json.Marshal(media)

		if err != nil {
			return err
		}

		if err := s.accountsService.UpdateAccountData(ctx, accountID, data); err != nil {
			return err
		}
	}

	return nil
}

func (s *feedsService) UpdateFeed(
	ctx context.Context,
	userID int,
	accountUUID string,
	updateFeedDTO *dto.UpdateFeedDTO,
) (*out.FeedDetailDTO, error) {
	account, err := s.accountsService.GetAccountByUUID(ctx, userID, accountUUID)

	if err != nil {
		return nil, err
	}

	plan, err := s.plansService.GetPlanByUserID(ctx, userID)

	if err != nil {
		return nil, err
	}

	var updateAccount bool

	if updateFeedDTO.Name.Set {
		updateAccount = true
		account.Name = &updateFeedDTO.Name.String
	}

	if updateFeedDTO.DomainWhitelist != nil {
		updateAccount = true
		account.DomainWhitelist = updateFeedDTO.DomainWhitelist
	}

	if updateFeedDTO.Items.Set {
		updateAccount = true
		items := int(updateFeedDTO.Items.Int64)

		planItemsValue := plan.Restrictions.GetMaxItems(account.Provider)

		if items > planItemsValue {
			return nil, errors.
				New(
					`Limit reached. The maximum value for "%s" is %s`,
					"Items",
					strconv.Itoa(planItemsValue),
				).
				SetCode(errors.ErrorCodePlanLimitReached)
		}

		account.Items = items
	}

	var providerAccountOptions interface{}

	provider, err := s.getProvider(account.Provider)

	if err != nil {
		return nil, err
	}

	if updateFeedDTO.Type.Set {
		updateAccount = true

		if !account.IsChangeTypeAvailable() {
			return nil, errors.New("An account type has already been selected. Can't change account type")
		}

		if !provider.IsTypeSupported(ctx, entity.AccountType(updateFeedDTO.Type.Int64)) {
			return nil, errors.New("Type not supported by this feed")
		}

		if err := account.SetAccountType(entity.AccountType(updateFeedDTO.Type.Int64)); err != nil {
			return nil, err
		}
	}

	if updateAccount || updateFeedDTO.Options.Set {
		if err := database.WithTransaction(
			s.db, ctx, func(tx *sqlx.Tx) error {
				if updateAccount {
					if err := s.accountsService.UpdateAccountTx(ctx, tx, account); err != nil {
						return err
					}
				}

				if updateFeedDTO.Options.Set {
					providerAccountOptions, err = provider.Update(
						ctx,
						tx,
						account,
						updateFeedDTO.Options.JSONText,
					)

					if err != nil {
						return err
					}
				}

				return nil
			},
		); err != nil {
			s.logger.WithCtx(ctx).WithError(err).Error("Failed to update account")
			return nil, errors.New(`Failed to update accounts`)
		}
	}

	if !updateFeedDTO.Options.Set {
		providerAccountOptions, err = provider.Get(ctx, account.ID)

		if err != nil {
			return nil, err
		}
	}

	if (updateAccount || updateFeedDTO.Options.Set) && account.IsStatusConfigure() {
		available, err := provider.IsAvailableForActivation(ctx, account.ID)

		if err != nil {
			return nil, err
		}

		if available {
			if err := s.ActivateFeed(ctx, userID, accountUUID); err != nil {
				return nil, err
			}

			go s.tasks.UpdateFeedData(account.ID)
		}
	}

	return out.NewFeedDetailDTO(account, provider.GetSupportedTypes(), providerAccountOptions), nil
}

func (s *feedsService) ActivateFeed(ctx context.Context, userID int, accountUUID string) error {
	account, err := s.accountsService.GetAccountByUUID(ctx, userID, accountUUID)

	if err != nil {
		return err
	}

	if account.Status != entity.AccountStatusConfigure {
		return errors.New(`To activate the feed, the status must be "Configurable"`)
	}

	provider, err := s.getProvider(account.Provider)

	if err != nil {
		return err
	}

	available, err := provider.IsAvailableForActivation(ctx, account.ID)

	if err != nil {
		return errors.NewWrap(err, "Failed to check available for activation")
	}

	if !available {
		return errors.New("The feed cannot be activated")
	}

	return s.accountsService.UpdateAccountStatus(ctx, account.ID, entity.AccountStatusActive, nil)
}

func (s *feedsService) ResetFeedType(ctx context.Context, userID int, accountUUID string) error {
	account, err := s.accountsService.GetAccountByUUID(ctx, userID, accountUUID)

	if err != nil {
		return err
	}

	if account.Type == nil {
		return errors.New("The type feed has not been selected yet")
	}

	provider, err := s.getProvider(account.Provider)

	if err != nil {
		return err
	}

	if err := database.WithTransaction(
		s.db, ctx, func(tx *sqlx.Tx) error {
			if err := provider.OnResetType(
				ctx,
				tx,
				account.ID,
				*account.Type,
			); err != nil && err != errors.ErrorMethodNotSupported {
				return err
			}

			account.Type = nil

			if err := s.accountsService.UpdateAccountTx(ctx, tx, account); err != nil {
				return err
			}

			return nil
		},
	); err != nil {
		s.logger.WithCtx(ctx).WithError(err).Error("Failed to reset feed type")
		return errors.New("Failed to reset feed type")
	}

	return nil
}
