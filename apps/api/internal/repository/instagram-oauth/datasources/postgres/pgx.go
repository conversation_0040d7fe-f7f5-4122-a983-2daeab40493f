package postgres

import (
	"context"
	"fmt"
	"github.com/Masterminds/squirrel"
	trmpgx "github.com/avito-tech/go-transaction-manager/drivers/pgxv5/v2"
	"github.com/feedspring/feedspring-api/apps/api/internal/entity"
	"github.com/feedspring/feedspring-api/apps/api/internal/errors"
	instagramoauthrepo "github.com/feedspring/feedspring-api/apps/api/internal/repository/instagram-oauth"
	"github.com/jackc/pgx/v5"
	"github.com/jackc/pgx/v5/pgxpool"
)

const tableName = "instagram_oauth_accounts"

type Datasource struct {
	pool             *pgxpool.Pool
	txGetter         *trmpgx.CtxGetter
	secretPassphrase string
}

var _ instagramoauthrepo.Repository = (*Datasource)(nil)
var sq = squirrel.StatementBuilder.PlaceholderFormat(squirrel.Dollar)

func NewDatasource(pool *pgxpool.Pool, secretPassphrase string) *Datasource {
	return &Datasource{
		pool:             pool,
		txGetter:         trmpgx.DefaultCtxGetter,
		secretPassphrase: secretPassphrase,
	}
}

func (d *Datasource) CreateInstagramOAuth(ctx context.Context, instagram *entity.InstagramOAuthAccount) error {
	query := `
INSERT INTO instagram_oauth_accounts (
	instagram_id,
	username,
	account_id,
	available_steps,
	access_token,
	expired_at
)
VALUES (
	$1, 
	$2, 
	$3, 
	$4,
	PGP_SYM_ENCRYPT($5, $7),
	$6
)
RETURNING id, created_at
`

	tx := d.txGetter.DefaultTrOrDB(ctx, d.pool)
	row := tx.QueryRow(
		ctx,
		query,
		instagram.InstagramID,
		instagram.Username,
		instagram.AccountID,
		instagram.AvailableSteps,
		instagram.AccessToken,
		instagram.ExpiredAt,
		d.secretPassphrase,
	)

	if err := row.Scan(&instagram.ID, &instagram.CreatedAt); err != nil {
		return fmt.Errorf("create instagram oauth: %w", err)
	}

	return nil
}

func (d *Datasource) GetInstagramOAuthByAccountID(ctx context.Context, accountID int) (*entity.InstagramOAuthAccount, error) {
	query := `
        SELECT 
            id, instagram_id, username, account_id, available_steps,
            PGP_SYM_DECRYPT(access_token::bytea, $2) as access_token,
            expired_at, created_at, updated_at
        FROM instagram_oauth_accounts
        WHERE account_id = $1
    `

	tx := d.txGetter.DefaultTrOrDB(ctx, d.pool)
	row := tx.QueryRow(ctx, query, accountID, d.secretPassphrase)

	var instagram entity.InstagramOAuthAccount
	err := row.Scan(
		&instagram.ID,
		&instagram.InstagramID,
		&instagram.Username,
		&instagram.AccountID,
		&instagram.AvailableSteps,
		&instagram.AccessToken,
		&instagram.ExpiredAt,
		&instagram.CreatedAt,
		&instagram.UpdatedAt,
	)

	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			return nil, nil
		}
		return nil, fmt.Errorf("get instagram oauth by account id: %w", err)
	}

	return &instagram, nil
}

func (d *Datasource) UpdateInstagramOAuth(ctx context.Context, instagram *entity.InstagramOAuthAccount) error {
	query := `
        UPDATE instagram_oauth_accounts
        SET available_steps = $1,
            access_token = PGP_SYM_ENCRYPT($2, $4),
            expired_at = $3,
            updated_at = NOW()
        WHERE id = $5
    `

	tx := d.txGetter.DefaultTrOrDB(ctx, d.pool)
	result, err := tx.Exec(
		ctx,
		query,
		instagram.AvailableSteps,
		instagram.AccessToken,
		instagram.ExpiredAt,
		d.secretPassphrase,
		instagram.ID,
	)

	if err != nil {
		return fmt.Errorf("update instagram oauth: %w", err)
	}

	if result.RowsAffected() == 0 {
		return errors.New("instagram oauth account not found")
	}

	return nil
}
