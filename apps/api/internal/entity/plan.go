package entity

import (
	"database/sql/driver"
	"encoding/json"
	"time"

	"github.com/google/uuid"
)

type (
	Plan struct {
		ID   int       `db:"id"`
		UUID uuid.UUID `db:"uuid"`

		Name   string  `db:"name"`
		Period *string `db:"period"`
		Level  int     `db:"level"`

		// Price ID from Stripe
		// https://stripe.com/docs/api/prices
		PriceID *string `db:"price_id"`

		Default      bool         `db:"default"`
		Restrictions Restrictions `db:"restrictions"`

		CreatedAt time.Time `db:"created_at"`
		UpdatedAt time.Time `db:"updated_at"`
	}

	Restrictions struct {
		Views int `json:"views"`
		Feeds int `json:"feeds"`

		TiktokItems        int `json:"tiktokItems"`
		DribbbleItems      int `json:"dribbbleItems"`
		InstagramItems     int `json:"instagramItems"`
		GoogleReviewsItems int `json:"googleReviewsItems"`

		TiktokCache        int64 `json:"tiktokCache"`
		DribbbleCache      int64 `json:"dribbbleCache"`
		GoogleReviewsCache int64 `json:"googleReviewsCache"`
		InstagramCache     int64 `json:"instagramCache"`
	}
)

func (r *Restrictions) GetCache(providerName string) time.Duration {
	cache := int64(time.Hour * 12 / time.Second)

	switch providerName {
	case "instagram", "instagram-oauth":
		cache = r.InstagramCache
	case "google-reviews":
		cache = r.GoogleReviewsCache
	case "dribbble":
		cache = r.DribbbleCache
	case "tiktok":
		cache = r.TiktokCache
	}

	return time.Duration(cache) * time.Second
}

func (r *Restrictions) GetMaxItems(providerName string) int {
	switch providerName {
	case "instagram", "instagram-oauth":
		return r.InstagramItems
	case "google-reviews":
		return r.GoogleReviewsItems
	case "dribbble":
		return r.DribbbleItems
	case "tiktok":
		return r.TiktokItems
	default:
		return 12
	}
}

func (r *Restrictions) Value() (driver.Value, error) {
	return json.Marshal(r)
}

func (r *Restrictions) Scan(value interface{}) error {
	var data []byte
	switch v := value.(type) {
	case string:
		data = []byte(v)
	case []byte:
		data = v
	}
	return json.Unmarshal(data, r)
}
