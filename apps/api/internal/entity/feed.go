package entity

import (
	"time"

	"github.com/google/uuid"

	"github.com/jmoiron/sqlx/types"
)

type (
	FeedStatus int
)

const (
	FeedStatusActive FeedStatus = iota + 1
	FeedStatusInactive
	FeedStatusConfigure
	FeedStatusDisabled
)

type FeedType int

const (
	FeedTypeAttrs FeedType = iota + 1
	FeedTypeWidget
	FeedTypeApi
	FeedTypeWebflow
	FeedTypeFramer
)

type FeedProvider string

const (
	InstagramFeedProvider      FeedProvider = "instagram"
	GoogleReviewsFeedProvider  FeedProvider = "google-reviews"
	TiktokFeedProvider         FeedProvider = "tiktok"
	DribbbleFeedProvider       FeedProvider = "dribbble"
	InstagramOauthFeedProvider FeedProvider = "instagram-oauth"
)

type Feed struct {
	ID   int
	UUID uuid.UUID

	Provider        FeedProvider
	Type            *FeedType
	PublicKey       string
	DomainWhitelist string
	Items           int
	Name            string
	Status          FeedStatus
	StatusReason    *string
	PrevStatus      *int
	Meta            types.JSONText
	Options         types.JSONText
	Data            types.JSONText
	DataUpdatedAt   *time.Time
	UserID          int // legacy
	UserUUID        uuid.UUID
	CreatedAt       time.Time
	UpdatedAt       *time.Time
}

type UserFeedsCount struct {
	UserID   int
	UserUUID uuid.UUID
	Count    int
}

var UserFeedCountNil = UserFeedsCount{}
