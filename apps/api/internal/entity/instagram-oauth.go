package entity

import "time"

type InstagramOAuthAccount struct {
	ID             int       `db:"id"`
	InstagramID    string    `db:"instagram_id"`
	Username       string    `db:"username"`
	AccountID      int       `db:"account_id"`
	AccessToken    string    `db:"access_token"`
	ExpiredAt      time.Time `db:"expired_at"`
	AvailableSteps int       `db:"available_steps"`
	CreatedAt      time.Time `db:"created_at"`
	UpdatedAt      time.Time `db:"updated_at"`
}

func (g InstagramOAuthAccount) IsAccessTokenExpired() bool {
	return time.Now().After(g.ExpiredAt)
}
