package app

import (
	"github.com/feedspring/feedspring-api/apps/api/internal/delivery/gql"
	"github.com/feedspring/feedspring-api/apps/api/internal/delivery/gql/resolvers"
	"github.com/feedspring/feedspring-api/apps/api/internal/delivery/http/middleware/authentication/ports"
	"github.com/feedspring/feedspring-api/apps/api/internal/middlewares"
	instagramoauthrepo "github.com/feedspring/feedspring-api/apps/api/internal/repository/instagram-oauth"
	instagramoauthpostgres "github.com/feedspring/feedspring-api/apps/api/internal/repository/instagram-oauth/datasources/postgres"
	"github.com/feedspring/feedspring-api/apps/api/internal/service/feeds"
	"github.com/feedspring/feedspring-api/apps/api/internal/service/plans"
	"github.com/feedspring/feedspring-api/apps/api/internal/service/users"
	"github.com/feedspring/feedspring-api/apps/api/internal/service/views"
	"github.com/feedspring/feedspring-api/apps/api/pkg/auth0"
	"github.com/jackc/pgx/v5/pgxpool"
	"os"
	"strconv"

	admingql "github.com/feedspring/feedspring-api/apps/api/internal/delivery/admin-gql"
	admingqldataloader "github.com/feedspring/feedspring-api/apps/api/internal/delivery/admin-gql/dataloader"
	adminresolvers "github.com/feedspring/feedspring-api/apps/api/internal/delivery/admin-gql/resolvers"

	"github.com/feedspring/feedspring-api/apps/api/internal/config"
	"github.com/feedspring/feedspring-api/apps/api/internal/delivery/http"
	"github.com/feedspring/feedspring-api/apps/api/internal/queue"
	"github.com/feedspring/feedspring-api/apps/api/internal/repository"
	"github.com/feedspring/feedspring-api/apps/api/internal/scheduler"
	"github.com/feedspring/feedspring-api/apps/api/internal/service"
	"github.com/feedspring/feedspring-api/apps/api/pkg/cache"

	"github.com/feedspring/feedspring-storage/proto/pb"
	"go.uber.org/fx"
	"google.golang.org/grpc"

	viewsrepo "github.com/feedspring/feedspring-api/apps/api/internal/repository/views"
	viewspostgres "github.com/feedspring/feedspring-api/apps/api/internal/repository/views/datasources/postgres"

	feedsrepo "github.com/feedspring/feedspring-api/apps/api/internal/repository/feeds"
	feedspostgres "github.com/feedspring/feedspring-api/apps/api/internal/repository/feeds/datasources/postgres"

	usersrepo "github.com/feedspring/feedspring-api/apps/api/internal/repository/users"
	userspostgres "github.com/feedspring/feedspring-api/apps/api/internal/repository/users/datasources/postgres"

	plansrepo "github.com/feedspring/feedspring-api/apps/api/internal/repository/plans"
	planspostgres "github.com/feedspring/feedspring-api/apps/api/internal/repository/plans/datasources/postgres"
)

func NewApp(configPath string) *fx.App {
	options := []fx.Option{
		fx.Provide(
			NewConfig(configPath),
			NewLogger,
			NewRedis,
			cache.NewCache,
			NewCron,
			NewServer,
			NewDatabase,
			NewPgx,
			NewMailer,
			NewTokens,
			auth0.NewClientFx,
		),

		fx.Provide(
			fx.Annotate(service.NewJwtService, fx.As(new(ports.JwtService))),
		),

		// Queue
		queue.Module,

		// Repositories
		fx.Provide(
			repository.NewGoogleReviewsRepository,
			repository.NewInstagramRepository,
			repository.NewTiktokRepository,
			repository.NewDribbbleRepository,

			//

			repository.NewTokensRepository,
			repository.NewUsersRepository,
			repository.NewAccountsRepository,
			repository.NewPlansRepository,
			fx.Annotate(
				viewspostgres.NewDatasource,
				fx.As(new(viewsrepo.Repository)),
			),
			fx.Annotate(
				feedspostgres.NewDatasource,
				fx.As(new(feedsrepo.Repository)),
			),
			fx.Annotate(
				userspostgres.NewDatasource,
				fx.As(new(usersrepo.Repository)),
			),
			fx.Annotate(
				planspostgres.NewDatasource,
				fx.As(new(plansrepo.Repository)),
			),
			fx.Annotate(
				func(pool *pgxpool.Pool, config *config.Config) *instagramoauthpostgres.Datasource {
					return instagramoauthpostgres.NewDatasource(pool, config.EncryptKeys.InstagramOauth)
				},
				fx.As(new(instagramoauthrepo.Repository)),
			),
		),

		// Services
		fx.Provide(
			service.NewGoogleReviewsService,
			service.NewInstagramService,
			service.NewTiktokService,
			service.NewDribbbleService,

			func(config *config.Config) pb.ImagesServiceClient {
				conn, err := grpc.Dial(config.Apps.Storage.Addr, grpc.WithInsecure())

				if err != nil {
					panic(err)
				}

				return pb.NewImagesServiceClient(conn)
			},

			//

			service.NewTokensService,
			service.NewAccountsService,
			service.NewUsersService,
			service.NewAuthenticationService,
			service.NewPasswordService,
			service.NewEmailService,
			service.NewJwtService,
			service.NewFeedsService,
			views.NewViewsService,
			service.NewBillingService,
			service.NewPlansService,
			feeds.New,
			users.New,
			plans.New,
			service.New,
		),

		// Middleware
		fx.Provide(
			middlewares.NewAuth,
		),

		fx.Provide(
			admingqldataloader.NewFactory,
			resolvers.New,
			adminresolvers.New,
		),

		// Handlers
		fx.Invoke(
			RunMigrations,
			NewValidator,

			http.NewUserHandler,
			http.NewFeedsHandler,
			http.NewAccountsHandler,
			http.NewAuth,
			http.NewViewsHandler,
			http.NewBillingHandler,
			http.NewGoogleReviewsHandler,
			gql.New,
			admingql.New,
		),
	}

	// Scheduler
	if os.Getenv("REPLICA") == "1" {
		options = append(options, scheduler.Module)
	}

	loggerOn, _ := strconv.ParseBool(os.Getenv("DI_LOGGER"))

	if !loggerOn {
		options = append(options, fx.NopLogger)
	}

	return fx.New(options...)
}
