package instagram

import (
	"context"
	"fmt"
	"net/http"
	"net/url"

	"github.com/feedspring/feedspring-api/apps/api/internal/errors"
	"github.com/go-resty/resty/v2"
)

const (
	baseURL = "https://graph.instagram.com/v23.0"
)

type Client struct {
	config Config
}

type Config struct {
	ClientID     string
	ClientSecret string
}

func New(config Config) Client {
	return Client{
		config: config,
	}
}

func (c *Client) GetAuthorizationURL(state, redirectURI string) string {
	u, err := url.Parse("https://api.instagram.com/oauth/authorize")
	if err != nil {
		return ""
	}

	q := u.Query()
	q.Add("client_id", c.config.ClientID)
	q.Add("redirect_uri", redirectURI)
	q.Add("enable_fb_login", "1")
	q.Add("force_authentication", "0")
	q.Add("scope", "instagram_business_basic")
	q.Add("response_type", "code")
	q.Add("state", state)

	u.RawQuery = q.Encode()
	return u.String()
}

func (c *Client) GetAuthTokenByCode(ctx context.Context, code, redirectURI string) (*AuthTokenResponse, error) {
	restyClient := resty.New()

	var shortLivedTokenResult AuthTokenResponse
	res, err := restyClient.R().
		SetContext(ctx).
		SetResult(&shortLivedTokenResult).
		SetFormData(map[string]string{
			"client_id":     c.config.ClientID,
			"client_secret": c.config.ClientSecret,
			"grant_type":    "authorization_code",
			"redirect_uri":  redirectURI,
			"code":          code,
		}).
		Post("https://api.instagram.com/oauth/access_token")
	if err != nil {
		return nil, err
	}

	if res.StatusCode() != http.StatusOK {
		return nil, errors.New("Failed to get Instagram access token: %s", res.String())
	}

	var longLivedTokenResult AuthTokenResponse
	res, err = restyClient.R().
		SetContext(ctx).
		SetResult(&longLivedTokenResult).
		SetQueryParams(map[string]string{
			"grant_type":    "ig_exchange_token",
			"client_secret": c.config.ClientSecret,
			"access_token":  shortLivedTokenResult.AccessToken,
		}).
		Get("https://graph.instagram.com/access_token")
	if err != nil {
		return nil, err
	}

	if res.StatusCode() != http.StatusOK {
		return nil, errors.New("Failed to get Instagram long-lived access token: %s", res.String())
	}

	return &longLivedTokenResult, nil
}

func (c *Client) GetUserProfile(ctx context.Context, accessToken string) (*UserProfile, error) {
	restyClient := resty.New()

	var result UserProfile
	res, err := restyClient.R().
		SetContext(ctx).
		SetResult(&result).
		SetQueryParams(map[string]string{
			"fields":       "id,username,biography,followers_count,follows_count,media_count,profile_picture_url,name",
			"access_token": accessToken,
		}).
		Get(fmt.Sprintf("%s/me", baseURL))

	if err != nil {
		return nil, err
	}

	if res.StatusCode() != http.StatusOK {
		return nil, errors.New("Failed to get Instagram user profile: %s", res.String())
	}

	return &result, nil
}

func (c *Client) GetUserMedia(ctx context.Context, accessToken string, limit int) (*MediaResponse, error) {
	restyClient := resty.New()

	var result MediaResponse
	res, err := restyClient.R().
		SetContext(ctx).
		SetResult(&result).
		SetQueryParams(map[string]string{
			"fields":       "id,caption,media_url,permalink,thumbnail_url,timestamp,comments_count,like_count,is_comment_enabled,is_shared_to_feed,media_product_type,media_type,shortcode,username,owner,children{id,media_type,media_url}",
			"access_token": accessToken,
			//"limit":        fmt.Sprintf("%d", limit),
		}).
		Get("https://graph.instagram.com/v23.0/me/media")
	if err != nil {
		return nil, err
	}

	if res.StatusCode() != http.StatusOK {
		return nil, errors.New("Failed to get Instagram media: %s", res.String())
	}

	return &result, nil
}

func (c *Client) RefreshAccessToken(ctx context.Context, accessToken string) (*AuthTokenResponse, error) {
	restyClient := resty.New()

	var result AuthTokenResponse
	res, err := restyClient.R().
		SetContext(ctx).
		SetResult(&result).
		SetQueryParams(map[string]string{
			"grant_type":   "ig_refresh_token",
			"access_token": accessToken,
		}).
		Get("https://graph.instagram.com/refresh_access_token")
	if err != nil {
		return nil, err
	}

	if res.StatusCode() != http.StatusOK {
		return nil, errors.New("Failed to refresh Instagram access token")
	}

	return &result, nil
}
