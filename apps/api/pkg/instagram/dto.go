package instagram

type AuthTokenResponse struct {
	AccessToken string `json:"access_token"`
	TokenType   string `json:"token_type"`
	ExpiresIn   int64  `json:"expires_in"`
}

type UserProfile struct {
	ID             string `json:"id"`
	Username       string `json:"username"`
	FollowersCount int    `json:"followers_count"`
	FollowingCount int    `json:"following_count"`
	Name           string `json:"name"`
	ProfilePicURL  string `json:"profile_pic_url"`
	Biography      string `json:"biography"`
}

type MediaResponse struct {
	Data   []Media `json:"data"`
	Paging struct {
		Next     string `json:"next"`
		Previous string `json:"previous"`
	} `json:"paging"`
}

type Media struct {
	ID               string  `json:"id"`
	Caption          string  `json:"caption"`
	Permalink        string  `json:"permalink"`
	MediaURL         string  `json:"media_url"`
	ThumbnailURL     string  `json:"thumbnail_url,omitempty"`
	Timestamp        string  `json:"timestamp"`
	CommentsCount    int     `json:"comments_count"`
	LikeCount        int     `json:"like_count"`
	IsCommentEnabled bool    `json:"is_comment_enabled"`
	IsSharedToFeed   bool    `json:"is_shared_to_feed"`
	MediaProductType string  `json:"media_product_type"` // AD, FEED, STORY or REELS
	MediaType        string  `json:"media_type"`         // IMAGE, VIDEO, CAROUSEL_ALBUM
	ShortCode        string  `json:"shortcode"`
	Username         string  `json:"username"`
	Children         []Media `json:"children"`
}
