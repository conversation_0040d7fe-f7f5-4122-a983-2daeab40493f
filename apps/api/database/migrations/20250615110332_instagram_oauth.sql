-- +goose Up
-- +goose StatementBegin
SELECT 'up SQL query';

CREATE TABLE instagram_oauth_accounts (
    ID SERIAL PRIMARY KEY,
    instagram_id VARCHAR(255) NOT NULL,
    username VARCHAR(255) NOT NULL,
    account_id INTEGER NOT NULL REFERENCES accounts(id) ON DELETE CASCADE,
    access_token TEXT,
    expired_at TIMESTAMP WITH TIME ZONE,
    available_steps INTEGER NOT NULL DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);
-- +goose StatementEnd

-- +goose Down
-- +goose StatementBegin
SELECT 'down SQL query';
DROP TABLE instagram_oauth_accounts;
-- +goose StatementEnd
